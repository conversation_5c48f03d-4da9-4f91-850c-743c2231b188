"use client";

import Link from "next/link";
import logoImg from "@/images/logo.png";
import { imagePath } from "@/lib/api";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import React from "react";

/**
 * Menu Configuration System
 * 
 * This system allows admins to control the visibility of menu items in the vendor sidebar.
 * Each menu item can be enabled/disabled independently, and sub-items can also be controlled.
 * 
 * Structure:
 * - MenuItemConfig: Defines the structure of individual menu items
 * - VendorMenuConfig: Defines the overall menu configuration
 * - defaultVendorMenuConfig: Contains the default configuration for all menu items
 * 
 * Usage:
 * 1. To disable a menu item, set its 'enabled' property to false
 * 2. To disable a sub-item, set its 'enabled' property to false in the parent's subItems
 * 3. To add a new menu item, add it to the defaultVendorMenuConfig object
 * 
 * Future Improvements:
 * - Store configuration in database
 * - Add admin interface for managing menu items
 * - Add role-based menu visibility
 * - Add dynamic menu items based on user permissions
 */

// Menu item configuration interface
interface MenuItemConfig {
  enabled: boolean;      // Controls visibility of the menu item
  label: string;         // Display text for the menu item
  path: string;          // URL path for the menu item
  icon?: React.ReactNode; // Optional icon component
  subItems?: {          // Optional sub-items configuration
    [key: string]: {
      enabled: boolean;  // Controls visibility of the sub-item
      label: string;     // Display text for the sub-item
      path: string;      // URL path for the sub-item
      icon?: React.ReactNode; // Optional icon component
    };
  };
}

// Vendor menu configuration interface
interface VendorMenuConfig {
  [key: string]: MenuItemConfig;
}

// Default vendor menu configuration
// This can be replaced with a database-driven configuration in the future
const defaultVendorMenuConfig: VendorMenuConfig = {
  dashboard: {
    enabled: true,
    label: "Dashboard",
    path: "/",
    icon: (
      <svg
        stroke="currentColor"
        fill="none"
        strokeWidth="2"
        viewBox="0 0 24 24"
        strokeLinecap="round"
        strokeLinejoin="round"
        height="20"
        width="20"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="7" height="7" x="3" y="3" rx="1"></rect>
        <rect width="7" height="7" x="14" y="3" rx="1"></rect>
        <rect width="7" height="7" x="14" y="14" rx="1"></rect>
        <rect width="7" height="7" x="3" y="14" rx="1"></rect>
      </svg>
    ),
  },
  sections: {
    enabled: true,
    label: "Sections & lots",
    path: "/sections",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M2.25 7.125C2.25 6.504 2.754 6 3.375 6h6c.621 0 1.125.504 1.125 1.125v3.75c0 .621-.504 1.125-1.125 1.125h-6a1.125 1.125 0 0 1-1.125-1.125v-3.75ZM14.25 8.625c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v8.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-8.25ZM3.75 16.125c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-2.25Z"
        />
      </svg>
    ),
  },
  campaigns: {
    enabled: true,
    label: "Campaigns",
    path: "/campaigns",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
        />
      </svg>
    ),
  },
  ratings: {
    enabled: true,
    label: "Ratings",
    path: "/ratings",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"
        />
      </svg>
    ),
  },
  hours: {
    enabled: true,
    label: "Hours",
    path: "/hours",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
        />
      </svg>
    ),
    subItems: {
      dutyRoster: {
        enabled: true,
        label: "Duty roster",
        path: "/roster",
      },
      workHours: {
        enabled: true,
        label: "Work hours",
        path: "/hours",
      },
    },
  },
  orders: {
    enabled: true,
    label: "Orders",
    path: "/orders",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
        />
      </svg>
    ),
    subItems: {
      ordersList: {
        enabled: true,
        label: "Orders",
        path: "/orders",
      },
      pendingRequests: {
        enabled: true,
        label: "Pending requests",
        path: "/temp-orders",
      },
      payments: {
        enabled: true,
        label: "Payments",
        path: "/payments",
      },
      newOrder: {
        enabled: true,
        label: "New order",
        path: "/orders/create",
      },
    },
  },
  bills: {
    enabled: false,
    label: "Bills",
    path: "/bills",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z"
        />
      </svg>
    ),
    subItems: {
      myBills: {
        enabled: true,
        label: "My Bills",
        path: "/bills",
      },
    },
  },
  users: {
    enabled: true,
    label: "User Accounts",
    path: "#",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
        />
      </svg>
    ),
    subItems: {
      staff: {
        enabled: true,
        label: "Staff",
        path: "/staff",
      },
      customers: {
        enabled: true,
        label: "Customers",
        path: "/customers",
      },
    },
  },
  products: {
    enabled: true,
    label: "Products",
    path: "/products",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
        />
      </svg>
    ),
    subItems: {
      productsList: {
        enabled: true,
        label: "Products",
        path: "/products",
      },
      addProduct: {
        enabled: true,
        label: "Add Product",
        path: "/products/create",
      },
      tags: {
        enabled: true,
        label: "Tags",
        path: "/tags",
      },
      productTemplates: {
        enabled: true,
        label: "Product templates",
        path: "/product-templates",
      },
      modifierOptions: {
        enabled: true,
        label: "Modifier options",
        path: "/products/modifiers",
      },
      packagingOptions: {
        enabled: true,
        label: "Packaging options",
        path: "/products/packaging-options",
      },
      formTemplates: {
        enabled: true,
        label: "Form Templates",
        path: "/products/forms/templates",
      },
    },
  },
  messages: {
    enabled: true,
    label: "Message Center",
    path: "/messages",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
        />
      </svg>
    ),
    subItems: {
      messagesList: {
        enabled: true,
        label: "Messages",
        path: "/messages"
      },
      messageTemplates: {
        enabled: true,
        label: "Message templates",
        path: "/messages/templates"
      },
      groups: {
        enabled: true,
        label: "Groups",
        path: "/messages/groups"
      },
      systemActions: {
        enabled: false,
        label: "System Actions",
        path: "/messages/system-actions"
      }
    }
  },
  delivery: {
    enabled: true,
    label: "Delivery Management",
    path: "/service-areas",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 1-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m6.75 4.5v-3a1.5 1.5 0 0 1 1.5-1.5h3a1.5 1.5 0 0 1 1.5 1.5v3m-6 0h4.5m0 0h1.875a1.125 1.125 0 0 0 1.125-1.125V9.375a1.125 1.125 0 0 0-1.125-1.125H14.25m1.5 4.5V8.25a2.25 2.25 0 0 0-2.25-2.25H9.375a1.125 1.125 0 0 0-1.125 1.125v1.5m1.5-1.5h1.5m0 0v1.5m0 0h1.5m-1.5 0v3M9 10.5h1.5"
        />
      </svg>
    ),
    subItems: {
      serviceAreas: {
        enabled: true,
        label: "Service Areas",
        path: "/service-areas",
      },
      deliverySettings: {
        enabled: true,
        label: "Delivery Settings",
        path: "/delivery-settings",
      },
    },
  }
};

interface User {
  id: string;
  title: string;
  firstName: string;
  lastName: string;
  gender: string | null;
  dob: string | null;
  email: string;
  phone: string;
  idpass: string;
  rememberMeToken: string | null;
  details: string | null;
  location: Record<string, any> | null;
  geom: string | null;
  avatar: AttachmentContract | null;
  createdAt: string;
  updatedAt: string;
  name: string;
  status: string;
  avatarUrl: string;
  initials: string;
  devices: Device[];
  roles: Role[];
  permissions: Permission[];
  notifications: DatabaseNotification[];
  identifier: string;
  online: boolean;
  vendorId: string;
}

// Admin menu configuration interface
interface AdminMenuItemConfig {
  enabled: boolean;
  label: string;
  path: string;
  icon?: React.ReactNode;
  subItems?: {
    [key: string]: {
      enabled: boolean;
      label: string;
      path: string;
      icon?: React.ReactNode;
    };
  };
}

interface AdminMenuConfig {
  [key: string]: AdminMenuItemConfig;
}

// Default admin menu configuration
const defaultAdminMenuConfig: AdminMenuConfig = {
  dashboard: {
    enabled: true,
    label: "Dashboard",
    path: "/",
    icon: (
      <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="7" height="7" x="3" y="3" rx="1"></rect>
              <rect width="7" height="7" x="14" y="3" rx="1"></rect>
              <rect width="7" height="7" x="14" y="14" rx="1"></rect>
              <rect width="7" height="7" x="3" y="14" rx="1"></rect>
            </svg>
    ),
  },
  notifications: {
    enabled: true,
    label: "Notifications",
    path: "/admin/notifications-dashboard",
    icon: (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"
              />
            </svg>
    ),
  },
  tasks: {
    enabled: true,
    label: "Task management",
    path: "/tasks",
    icon: (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M6 6.878V6a2.25 2.25 0 0 1 2.25-2.25h7.5A2.25 2.25 0 0 1 18 6v.878m-12 0c.235-.083.487-.128.75-.128h10.5c.263 0 .515.045.75.128m-12 0A2.25 2.25 0 0 0 4.5 9v.878m13.5-3A2.25 2.25 0 0 1 19.5 9v.878m0 0a2.246 2.246 0 0 0-.75-.128H5.25c-.263 0-.515.045-.75.128m15 0A2.25 2.25 0 0 1 21 12v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6c0-.98.626-1.813 1.5-2.122"
              />
            </svg>
    ),
  },
  services: {
    enabled: true,
    label: "Service management",
    path: "/services",
    icon: (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z"
              />
            </svg>
    ),
  },
  usersManagement: {
    enabled: true,
    label: "Users Management",
    path: "/admin/users",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
      </svg>
    ),
  },
  serviceConfigurations: {
    enabled: true,
    label: "Service Configurations",
    path: "/admin/service-configurations",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a6.759 6.759 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"
        />
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
      </svg>
    ),
    subItems: {
      configurationsList: {
        enabled: true,
        label: "Configurations",
        path: "/admin/service-configurations",
      },
      createConfiguration: {
        enabled: true,
        label: "Create Configuration",
        path: "/admin/service-configurations/create",
      },
    },
  },
  durations: {
    enabled: true,
    label: "Duration Management",
    path: "/admin/durations",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
        />
      </svg>
    ),
    subItems: {
      durationsList: {
        enabled: true,
        label: "Duration Templates",
        path: "/admin/durations",
      },
      createDuration: {
        enabled: true,
        label: "Create Duration",
        path: "/admin/durations/create",
      },
    },
  },
  campaigns: {
    enabled: true,
    label: "Campaigns",
    path: "/campaigns",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
        />
      </svg>
    ),
  },
  orders: {
    enabled: true,
    label: "Orders",
    path: "/orders",
    icon: (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"
              />
            </svg>
    ),
    subItems: {
      ordersList: {
        enabled: true,
        label: "Orders",
        path: "/orders",
      },
      pendingRequests: {
        enabled: true,
        label: "Pending requests",
        path: "/temp-orders",
      },
      newOrder: {
        enabled: true,
        label: "New order",
        path: "/orders/create",
      },
    },
  },
  customers: {
    enabled: true,
    label: "Customers",
    path: "/customers",
    icon: (
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
    ),
    subItems: {
      customersList: {
        enabled: true,
        label: "Customers",
        path: "/customers",
      },
      addCustomer: {
        enabled: true,
        label: "Add Customer",
        path: "/customers/create",
      },
    },
  },
  products: {
    enabled: true,
    label: "Products",
    path: "/products",
    icon: (
      <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
          d="M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
        />
                  </svg>
    ),
    subItems: {
      productsList: {
        enabled: true,
        label: "Products",
        path: "/products",
      },
      addProduct: {
        enabled: true,
        label: "Add Product",
        path: "/products/create",
      },
      tags: {
        enabled: true,
        label: "Tags",
        path: "/tags",
      },
      productTemplates: {
        enabled: true,
        label: "Product templates",
        path: "/product-templates",
      },
      modifierOptions: {
        enabled: true,
        label: "Modifier options",
        path: "/products/modifiers",
      },
      packagingOptions: {
        enabled: true,
        label: "Packaging options",
        path: "/products/packaging-options",
      },
      formTemplates: {
        enabled: true,
        label: "Form Templates",
        path: "/products/forms/templates",
      },
    },
  },
  vendors: {
    enabled: true,
    label: "Vendors",
    path: "/vendors",
    icon: (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
          d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z"
              />
            </svg>
    ),
    subItems: {
      vendorsList: {
        enabled: true,
        label: "Vendors",
        path: "/vendors",
      },
      addVendor: {
        enabled: true,
        label: "Add Vendor",
        path: "/vendors/create",
      },
      bulkUpload: {
        enabled: true,
        label: "Bulk Upload",
        path: "/admin/vendors/bulk-upload",
      },
      specialization: {
        enabled: true,
        label: "Specialization",
        path: "/specialization",
      },
    },
  },
  messages: {
    enabled: true,
    label: "Message Center",
    path: "/messages",
    icon: (
      <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
          d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
        />
                  </svg>
    ),
    subItems: {
      messagesList: {
        enabled: false,
        label: "Messages",
        path: "/messages"
      },
      messageTemplates: {
        enabled: false,
        label: "Message templates",
        path: "/messages/templates"
      },
      groups: {
        enabled: false,
        label: "Groups",
        path: "/messages/groups"
      },
      systemActions: {
        enabled: true,
        label: "System Actions",
        path: "/messages/system-actions"
      }
    },
  },
  deliveryManagement: {
    enabled: true,
    label: "Delivery Management",
    path: "/admin/delivery-management/vendors",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 1-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m6.75 4.5v-3a1.5 1.5 0 0 1 1.5-1.5h3a1.5 1.5 0 0 1 1.5 1.5v3m-6 0h4.5m0 0h1.875a1.125 1.125 0 0 0 1.125-1.125V9.375a1.125 1.125 0 0 0-1.125-1.125H14.25m1.5 4.5V8.25a2.25 2.25 0 0 0-2.25-2.25H9.375a1.125 1.125 0 0 0-1.125 1.125v1.5m1.5-1.5h1.5m0 0v1.5m0 0h1.5m-1.5 0v3M9 10.5h1.5"
        />
      </svg>
    ),
    subItems: {
      vendors: {
        enabled: true,
        label: "Vendors",
        path: "/admin/delivery-management/vendors",
      },
      verification: {
        enabled: true,
        label: "Verification Queue",
        path: "/admin/delivery-management/verification",
      },
      serviceAreas: {
        enabled: true,
        label: "Service Areas",
        path: "/admin/delivery-management/service-areas",
      },
      analytics: {
        enabled: true,
        label: "Analytics",
        path: "/admin/delivery-management/analytics",
      },
      settings: {
        enabled: true,
        label: "Settings",
        path: "/admin/delivery-management/settings",
      },
    },
  },
};

export default function PortalSidebar({
  user,
  tasks,
  branch,
  vendor,
  logoutUser,
}: {
  user: User | undefined;
  tasks?: PaginatedData<Task>;
  branch?: Branch;
  vendor?: Vendor;
  logoutUser: () => Promise<void>;
}) {
  const pathname = usePathname();
  const [activeMenu, setActiveMenu] = useState<string>("");

  useEffect(() => {
    // Save branch vendorId to local storage if it exists
    if (branch && branch.vendorId) {
      localStorage.setItem("branchVendorId", branch.vendorId);
    }
  }, [branch]);

  // Log the branch vendorId

  // Rest of your component code...
  const DropCursor = ({ menu = "", path = "" }) => (
    <>
      {activeMenu === menu ? (
        <svg
          xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="ms-auto h-4 w-4"
        >
          <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
            d="m4.5 15.75 7.5-7.5 7.5 7.5"
          />
                  </svg>
      ) : (
        <svg
          xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="ms-auto h-4 w-4"
        >
          <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
            d="m8.25 4.5 7.5 7.5-7.5 7.5"
          />
                  </svg>
      )}
    </>
  );

  const AdminMenu = () => {
    // Get menu configuration from props or use default
    const menuConfig = defaultAdminMenuConfig;

    return (
      <div
        data-simplebar="init"
        className="h-[calc(100%-160px)] overflow-y-scroll pb-8 scrollbar-thin scrollbar-webkit"
      >
        <ul className="hs-accordion-group mb-5 flex w-full flex-col gap-1.5 overflow-y-auto p-4">
          {Object.entries(menuConfig).map(([key, item]) => {
            if (!item.enabled) return null;

            if (!item.subItems) {
              return (
                <li key={key} className="">
                <Link
                    className={
                      "active flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
                      (pathname === item.path ? "bg-default-100 text-primary" : "")
                    }
                    data-menu-key={`${key}-page`}
                    href={item.path}
                  >
                    {item.icon}
                    {item.label}
                </Link>
              </li>
              );
            }

            return (
              <li key={key} className="hs-accordion group">
          <button
            className={
              "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm hover:bg-default-100 " +
                    (pathname.startsWith(item.path) || activeMenu === key
                ? "bg-default-100 text-primary"
                : "text-default-700")
            }
            aria-expanded="false"
                  data-menu-key={key}
                  onClick={() => setActiveMenu(activeMenu === key ? "" : key)}
                >
                  {item.icon}
                  {item.label}
                  <DropCursor menu={key} />
          </button>
          <div
            className={
              "hs-accordion-content w-full overflow-hidden transition-[height] " +
                    (pathname.startsWith(item.path) || activeMenu === key
                ? "block"
                : "hidden")
            }
          >
            <ul className="mt-2 space-y-2">
                    {Object.entries(item.subItems).map(([subKey, subItem]) => {
                      if (!subItem.enabled) return null;
                      return (
                        <li key={subKey}>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                            data-menu-key={`${key}-${subKey}`}
                            href={subItem.path}
                >
                            {subItem.icon || (
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                            )}
                            {subItem.label}
                </Link>
              </li>
                      );
                    })}
            </ul>
          </div>
        </li>
            );
          })}

        {tasks?.data?.map((task) => (
          <li key={task.id} className={`hs-accordion group group/${task.id}`}>
            <button
              className={
                "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm hover:bg-default-100 " +
                (pathname.startsWith(`/tasks/${task.id}`) ||
                activeMenu === task.id
                  ? "bg-default-100 text-primary"
                  : "text-default-700")
              }
              data-menu-key="extra-pages-starter"
              onClick={() =>
                setActiveMenu(activeMenu === task.id ? "" : task.id)
              }
            >
              {task.name}
              <DropCursor menu={task.id} />
            </button>

            <div
              className={
                `hs-accordion-content ml-4 w-full overflow-y-scroll transition-[height] ` +
                (pathname.startsWith(`/tasks/${task.id}`) ||
                activeMenu === task.id
                  ? "block"
                  : "hidden")
              }
            >
              <ul className="mt-2 space-y-2 overflow-y-scroll">
                <li className="">
                  <Link
                    className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                    data-menu-key="extra-pages-starter"
                    href={`/tasks/${task.id}`}
                  >
                    Overview
                  </Link>
                </li>

                {task.services?.map((service) => (
                  <li key={service.id} className="group">
                    <Link
                      className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                      data-menu-key="extra-pages-starter"
                      href={`/services/${service.id}`}
                    >
                      {service.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
  };

  const VendorMenu = () => {
    // Get menu configuration from props or use default
    const menuConfig = defaultVendorMenuConfig;

    return (
    <div
      data-simplebar="init"
      className="h-[calc(100%-160px)] overflow-y-scroll pb-8 scrollbar-thin scrollbar-webkit"
    >
      <ul className="hs-accordion-group mb-5 flex w-full flex-col gap-1.5 overflow-y-auto p-4">
          {Object.entries(menuConfig).map(([key, item]) => {
            if (!item.enabled) return null;

            if (!item.subItems) {
              return (
                <li key={key} className="">
          <Link
            className={
              "active flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
                      (pathname === item.path ? "bg-default-100 text-primary" : "")
                    }
                    data-menu-key={`${key}-page`}
                    href={item.path}
                  >
                    {item.icon}
                    {item.label}
          </Link>
        </li>
              );
            }

            return (
              <li key={key} className="hs-accordion group">
          <button
            className={
              "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm hover:bg-default-100 " +
                    (pathname.startsWith(item.path) || activeMenu === key
                ? "bg-default-100 text-primary"
                      : "text-default-700")
            }
            aria-expanded="false"
            data-menu-key={key}
            onClick={() => setActiveMenu(activeMenu === key ? "" : key)}
          >
            {item.icon}
            {item.label}
            <DropCursor menu={key} />
          </button>
          <div
            className={
              "hs-accordion-content w-full overflow-hidden transition-[height] " +
                    (pathname.startsWith(item.path) || activeMenu === key
                ? "block"
                : "hidden")
            }
          >
            <ul className="mt-2 space-y-2">
                    {Object.entries(item.subItems).map(([subKey, subItem]) => {
                      if (!subItem.enabled) return null;
                      return (
                        <li key={subKey}>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                            data-menu-key={`${key}-${subKey}`}
                            href={subItem.path}
                          >
                            {subItem.icon || (
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                            )}
                            {subItem.label}
                </Link>
              </li>
                      );
                    })}
            </ul>
          </div>
        </li> 
            );
          })}
      </ul>
    </div>
  );
  };

  // Assign the initial role to the first role in the array
let userRole = user?.roles[0].name;
const userRoleLength = user?.roles.length;
// Check if the user has multiple roles
if (userRoleLength && userRoleLength > 1) {
  // Loop through each role to check for "vendor" or "admin"
  for (let i = 0; i < userRoleLength; i++) {
    if (user?.roles[i].name === "vendor") {
      userRole = "vendor"; // Assign "vendor" as the role if found
      break; // Exit the loop if "vendor" is found
    } 
    else if (user?.roles[i].name === "admin") {
      userRole = "admin"; // Assign "admin" as the role if "vendor" isn't found
    }
  }
}

  return (
    <div
      id="application-sidebar"
      className="hs-overlay hs-overlay-open:translate-x-0 hide-in-print fixed inset-y-0 start-0 z-[50] hidden w-64 -translate-x-full transform border-e border-default-200 bg-white transition-all duration-300 dark:bg-default-900 dark:text-white lg:bottom-0 lg:right-auto lg:block lg:translate-x-0"
    >
      <div className="sticky top-0 flex h-20 items-center justify-center border-b border-dashed border-default-200 px-6">
        <Link href="/">
          <Image
            src={vendor ? imagePath(vendor.logo?.url) : logoImg}
            height={60}
            width={100}
            alt="logo"
            className="flex w-20 dark:hidden"
          />

          <Image
            src={vendor ? imagePath(vendor.logo?.url) : logoImg}
            height={60}
            width={100}
            alt="logo"
            className="hidden w-20 dark:flex"
          />
        </Link>
      </div>

      {branch && (
        <div className="gap-1.5 px-8">
          <p className="mt-4 text-primary">{branch.name}</p>
        </div>
      )}

      {userRole === "vendor" ? <VendorMenu /> : <AdminMenu />}

      <ul className="fixed bottom-0 z-[150] flex w-64 flex-col gap-2 rounded-t-lg border-e border-default-200 bg-default-50 px-4 shadow-md">
        {branch && (
          <li>
            <Link
              className="flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary"
              data-menu-key="manage-page"
              href="/preferences"
            >
              <svg
                stroke="currentColor"
                fill="none"
                strokeWidth="2"
                viewBox="0 0 24 24"
                strokeLinecap="round"
                strokeLinejoin="round"
                height="20"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M20 7h-9"></path>
                <path d="M14 17H5"></path>
                <circle cx="17" cy="17" r="3"></circle>
                <circle cx="7" cy="7" r="3"></circle>
              </svg>
              Preferences
            </Link>
          </li>
        )}

        <li className="menu-item">
          <Link
            className="flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100"
            href="/profile"
          >
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M18 20a6 6 0 0 0-12 0"></path>
              <circle cx="12" cy="10" r="4"></circle>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
            My Profile
          </Link>
        </li>
        <li className="menu-item ">
          <a
            className="flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-red-500 hover:bg-red-400/10 hover:text-red-600"
            href="/logout"
            onClick={logoutUser}
          >
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16 17 21 12 16 7"></polyline>
              <line x1="21" x2="9" y1="12" y2="12"></line>
            </svg>
            Logout
          </a>
        </li>
      </ul>
    </div>
  );
}
