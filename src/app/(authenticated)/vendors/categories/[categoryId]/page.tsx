import { api, imagePath } from "@/lib/api";
import { Metadata } from "next";
import Link from "next/link";
import AdminVendorsEditForm from "./edit";
import { revalidatePath } from "next/cache";
import Image from "next/image";
import PaginatedTable from "@/components/table";
import VendorCategoryTools from "./tools";
import AdminVendorCategoryCreateVendorForm from "./create";

const getVendorCategory = async (categoryId: string) => {
  return await api.get<VendorCategory>(`vendor-categories/${categoryId}`);
};

export const generateMetadata = async ({
  params,
}: {
  params: { categoryId: string };
}): Promise<Metadata> => {
  const category = await getVendorCategory(params.categoryId);


  return {
    title: category?.name,
  };
};

export default async function AdminVendorCategoryIndex({
  params,
}: {
  params: { categoryId: string };
}) {
  const tasks = await api.get<PaginatedData<Task>>("/tasks");

  const vendors = await api.get<PaginatedData<Vendor>>(
    `vendor-categories/${params.categoryId}/vendors`,
    { per: 30 },
  );

  const storeVendor = async (vendor: FormData) => {
    "use server";

    await api.post("vendors", vendor);

    revalidatePath(`/vendors/categories/${params.categoryId}`);
  };

  const updateVendor = async (vendor: FormData) => {
    "use server";

    await api.put(`vendors/${vendor.get("id")}`, vendor);

    revalidatePath(`/vendors/categories/${params.categoryId}`);
  };

  return (
    <div className="py-4">
      <PaginatedTable<Vendor>
        records={vendors!}
        columns={[
          {
            id: "name",
            title: "Name",
            class:
              "flex items-center px-6 py-4 text-default-900 whitespace-nowrap dark:text-white",
            render: (vendor: Vendor) => (
              <>
                <Image
                  width={50}
                  height={50}
                  className="size-10 rounded-full"
                  src={imagePath(vendor.logo?.url)}
                  alt={vendor.name}
                />
                <Link className="pl-3" href={`/vendors/${vendor.id}`}>
                  <div className="text-base font-semibold">{vendor.name}</div>
                  <div className="font-normal text-default-500">
                    {vendor.email}
                  </div>
                </Link>
              </>
            ),
          },
          {
            id: "phone",
            title: "Phone",
          },
          {
            id: "email",
            title: "Email",
          },
          {
            id: "active",
            title: "Status",
            render: (vendor: Vendor) => (
              <div className="flex items-center">
                <div className="mr-2 h-2.5 w-2.5 rounded-full bg-green-500"></div>
                {vendor.active ? "Active" : "Inactive"}
              </div>
            ),
          },

          {
            id: "actions",
            title: "Actions",
            render: (vendor: Vendor) => (
              <AdminVendorsEditForm
                defaultValues={vendor}
                updateVendor={updateVendor}
              />
            ),
          },
        ]}
        path="admin/vendors"
        tools={
          <div className="mb-4 flex items-center justify-between bg-white px-4 dark:bg-default-900">
            <div className="w-2/3">
              <button
                id="dropdownActionButton"
                data-dropdown-toggle="dropdownAction"
                className="inline-flex items-center rounded-lg border border-default-300 bg-white px-3 py-1.5 text-sm font-medium text-default-500 hover:bg-default-100 focus:outline-none focus:ring-4 focus:ring-default-200 dark:border-default-600 dark:bg-default-800 dark:text-default-400 dark:hover:border-default-600 dark:hover:bg-default-700 dark:focus:ring-default-700"
                type="button"
              >
                <span className="sr-only">Action button</span>
                Action
                <svg
                  className="ml-2 h-3 w-3"
                  aria-hidden="true"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </button>
              <div
                id="dropdownAction"
                className="z-10 hidden w-44 divide-y divide-default-100 rounded-lg bg-white shadow dark:divide-default-600 dark:bg-default-700"
              >
                <ul
                  className="py-1 text-sm text-default-700 dark:text-default-200"
                  aria-labelledby="dropdownActionButton"
                >
                  <li>
                    <a
                      href="#"
                      className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                    >
                      Reward
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                    >
                      Promote
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                    >
                      Activate account
                    </a>
                  </li>
                </ul>
                <div className="py-1">
                  <a
                    href="#"
                    className="block px-4 py-2 text-sm text-default-700 hover:bg-default-100 dark:text-default-200 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Delete User
                  </a>
                </div>
              </div>
            </div>

            <div className="flex flex-1 space-x-2">
              <div>
                <label htmlFor="table-search" className="sr-only">
                  Search
                </label>
                <div className="relative">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <svg
                      className="h-5 w-5 text-default-500 dark:text-default-400"
                      aria-hidden="true"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                        clipRule="evenodd"
                      ></path>
                    </svg>
                  </div>
                  <input
                    type="text"
                    id="table-search-users"
                    className="block w-80  rounded-lg border border-default-300 bg-default-50 p-3 pl-10 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                    placeholder="Search for users"
                  />
                </div>
              </div>

              <Link
                href={`/vendors/create?vendorCategoryId=${params.categoryId}`}
                className="hover:bg-default-dark focus:ring-default-dark inline-flex items-center rounded-lg border border-primary bg-primary px-6 py-2 text-sm font-medium text-white focus:outline-none focus:ring-4"
              >
                Add vendor
              </Link>
            </div>
          </div>
        }
        title="Vendors"
      />
    </div>
  );
}
