'use server';

import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { ActionType, ActionTypeFormData } from "./components/types";

interface ApiResponse {
  types: ActionType[];
}

export async function getActionTypes() {
  try {
    const response = await api.get<ApiResponse>("action-types");
    if (!response) {
      return { data: [], error: 'No response from server' };
    }
    return { data: response.types, error: null };
  } catch (error) {
    console.error('Error fetching action types:', error);
    return { data: [], error: 'Failed to load action types' };
  }
}

export async function getActionType(id: string) {
  try {
    const response = await api.get<ActionType>(`action-types/${id}`);
    if (!response) {
      return { data: null, error: 'No response from server' };
    }
    return { data: response, error: null };
  } catch (error) {
    console.error('Error fetching action type:', error);
    return { data: null, error: 'Failed to load action type' };
  }
}

export async function createActionType(data: ActionTypeFormData): Promise<{ success: boolean; actionType?: ActionType; error?: string }> => {
  try {
    const actionType = await api.post<ActionType>("action-types", data);
    revalidatePath("/messages/system-actions");
    return { success: true, actionType: actionType || undefined };
  } catch (error) {
    console.error('Error creating action type:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create action type'
    };
  }
}

export async function updateActionType(id: string, data: ActionTypeFormData): Promise<{ success: boolean; actionType?: ActionType; error?: string }> => {
  try {
    const actionType = await api.put<ActionType>(`action-types/${id}`, data);
    revalidatePath("/messages/system-actions");
    revalidatePath(`/messages/system-actions/${id}`);
    return { success: true, actionType: actionType || undefined };
  } catch (error) {
    console.error('Error updating action type:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update action type'
    };
  }
}

export async function deleteActionType(id: string): Promise<{ success: boolean; error?: string }> => {
  try {
    await api.destroy(id, "action-types");
    revalidatePath("/messages/system-actions");
    return { success: true };
  } catch (error) {
    console.error('Error deleting action type:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete action type'
    };
  }
}