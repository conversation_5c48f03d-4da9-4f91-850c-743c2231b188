import toast from 'react-hot-toast';

export interface ToastOptions {
  loading?: string;
  success?: string;
  error?: string;
}

export interface EntityToastMessages {
  create: ToastOptions;
  update: ToastOptions;
  delete: ToastOptions;
  activate: ToastOptions;
  deactivate: ToastOptions;
  feature: ToastOptions;
  unfeature: ToastOptions;
  assign: ToastOptions;
  unassign: ToastOptions;
  upload: ToastOptions;
  send: ToastOptions;
}

// Default toast messages for different operations
const defaultMessages = {
  create: {
    loading: 'Creating...',
    success: 'Created successfully',
    error: 'Failed to create'
  },
  update: {
    loading: 'Updating...',
    success: 'Updated successfully',
    error: 'Failed to update'
  },
  delete: {
    loading: 'Deleting...',
    success: 'Deleted successfully',
    error: 'Failed to delete'
  },
  activate: {
    loading: 'Activating...',
    success: 'Activated successfully',
    error: 'Failed to activate'
  },
  deactivate: {
    loading: 'Deactivating...',
    success: 'Deactivated successfully',
    error: 'Failed to deactivate'
  },
  feature: {
    loading: 'Featuring...',
    success: 'Featured successfully',
    error: 'Failed to feature'
  },
  unfeature: {
    loading: 'Unfeaturing...',
    success: 'Unfeatured successfully',
    error: 'Failed to unfeature'
  },
  assign: {
    loading: 'Assigning...',
    success: 'Assigned successfully',
    error: 'Failed to assign'
  },
  unassign: {
    loading: 'Unassigning...',
    success: 'Unassigned successfully',
    error: 'Failed to unassign'
  },
  upload: {
    loading: 'Uploading...',
    success: 'Uploaded successfully',
    error: 'Failed to upload'
  },
  send: {
    loading: 'Sending...',
    success: 'Sent successfully',
    error: 'Failed to send'
  }
};

// Entity-specific toast messages
export const entityToastMessages: Record<string, Partial<EntityToastMessages>> = {
  vendor: {
    create: {
      loading: 'Creating vendor...',
      success: 'Vendor created successfully',
      error: 'Failed to create vendor'
    },
    update: {
      loading: 'Updating vendor...',
      success: 'Vendor updated successfully',
      error: 'Failed to update vendor'
    },
    delete: {
      loading: 'Deleting vendor...',
      success: 'Vendor deleted successfully',
      error: 'Failed to delete vendor'
    },
    activate: {
      loading: 'Activating vendor...',
      success: 'Vendor activated successfully',
      error: 'Failed to activate vendor'
    },
    deactivate: {
      loading: 'Deactivating vendor...',
      success: 'Vendor deactivated successfully',
      error: 'Failed to deactivate vendor'
    },
    feature: {
      loading: 'Featuring vendor...',
      success: 'Vendor featured successfully',
      error: 'Failed to feature vendor'
    },
    unfeature: {
      loading: 'Unfeaturing vendor...',
      success: 'Vendor unfeatured successfully',
      error: 'Failed to unfeature vendor'
    },
    upload: {
      loading: 'Uploading vendors...',
      success: 'Vendors uploaded successfully',
      error: 'Failed to upload vendors'
    }
  },
  product: {
    create: {
      loading: 'Creating product...',
      success: 'Product created successfully',
      error: 'Failed to create product'
    },
    update: {
      loading: 'Updating product...',
      success: 'Product updated successfully',
      error: 'Failed to update product'
    },
    delete: {
      loading: 'Deleting product...',
      success: 'Product deleted successfully',
      error: 'Failed to delete product'
    },
    activate: {
      loading: 'Activating product...',
      success: 'Product activated successfully',
      error: 'Failed to activate product'
    },
    deactivate: {
      loading: 'Deactivating product...',
      success: 'Product deactivated successfully',
      error: 'Failed to deactivate product'
    },
    feature: {
      loading: 'Featuring product...',
      success: 'Product featured successfully',
      error: 'Failed to feature product'
    },
    unfeature: {
      loading: 'Unfeaturing product...',
      success: 'Product unfeatured successfully',
      error: 'Failed to unfeature product'
    },
    upload: {
      loading: 'Uploading products...',
      success: 'Products uploaded successfully',
      error: 'Failed to upload products'
    }
  },
  user: {
    create: {
      loading: 'Creating user...',
      success: 'User created successfully',
      error: 'Failed to create user'
    },
    update: {
      loading: 'Updating user...',
      success: 'User updated successfully',
      error: 'Failed to update user'
    },
    delete: {
      loading: 'Deleting user...',
      success: 'User deleted successfully',
      error: 'Failed to delete user'
    },
    activate: {
      loading: 'Activating user...',
      success: 'User activated successfully',
      error: 'Failed to activate user'
    },
    deactivate: {
      loading: 'Deactivating user...',
      success: 'User deactivated successfully',
      error: 'Failed to deactivate user'
    }
  },
  service: {
    create: {
      loading: 'Creating service...',
      success: 'Service created successfully',
      error: 'Failed to create service'
    },
    update: {
      loading: 'Updating service...',
      success: 'Service updated successfully',
      error: 'Failed to update service'
    },
    delete: {
      loading: 'Deleting service...',
      success: 'Service deleted successfully',
      error: 'Failed to delete service'
    },
    activate: {
      loading: 'Activating service...',
      success: 'Service activated successfully',
      error: 'Failed to activate service'
    },
    deactivate: {
      loading: 'Deactivating service...',
      success: 'Service deactivated successfully',
      error: 'Failed to deactivate service'
    },
    feature: {
      loading: 'Featuring service...',
      success: 'Service featured successfully',
      error: 'Failed to feature service'
    },
    unfeature: {
      loading: 'Unfeaturing service...',
      success: 'Service unfeatured successfully',
      error: 'Failed to unfeature service'
    },
    assign: {
      loading: 'Assigning service...',
      success: 'Service assigned successfully',
      error: 'Failed to assign service'
    }
  },
  campaign: {
    create: {
      loading: 'Creating campaign...',
      success: 'Campaign created successfully',
      error: 'Failed to create campaign'
    },
    update: {
      loading: 'Updating campaign...',
      success: 'Campaign updated successfully',
      error: 'Failed to update campaign'
    },
    delete: {
      loading: 'Deleting campaign...',
      success: 'Campaign deleted successfully',
      error: 'Failed to delete campaign'
    },
    activate: {
      loading: 'Activating campaign...',
      success: 'Campaign activated successfully',
      error: 'Failed to activate campaign'
    },
    deactivate: {
      loading: 'Deactivating campaign...',
      success: 'Campaign deactivated successfully',
      error: 'Failed to deactivate campaign'
    }
  },
  message: {
    create: {
      loading: 'Creating message...',
      success: 'Message created successfully',
      error: 'Failed to create message'
    },
    update: {
      loading: 'Updating message...',
      success: 'Message updated successfully',
      error: 'Failed to update message'
    },
    delete: {
      loading: 'Deleting message...',
      success: 'Message deleted successfully',
      error: 'Failed to delete message'
    },
    send: {
      loading: 'Sending message...',
      success: 'Message sent successfully',
      error: 'Failed to send message'
    }
  },
  task: {
    create: {
      loading: 'Creating task...',
      success: 'Task created successfully',
      error: 'Failed to create task'
    },
    update: {
      loading: 'Updating task...',
      success: 'Task updated successfully',
      error: 'Failed to update task'
    },
    delete: {
      loading: 'Deleting task...',
      success: 'Task deleted successfully',
      error: 'Failed to delete task'
    },
    activate: {
      loading: 'Activating task...',
      success: 'Task activated successfully',
      error: 'Failed to activate task'
    },
    deactivate: {
      loading: 'Deactivating task...',
      success: 'Task deactivated successfully',
      error: 'Failed to deactivate task'
    }
  },
  'action-type': {
    create: {
      loading: 'Creating action type...',
      success: 'Action type created successfully',
      error: 'Failed to create action type'
    },
    update: {
      loading: 'Updating action type...',
      success: 'Action type updated successfully',
      error: 'Failed to update action type'
    },
    delete: {
      loading: 'Deleting action type...',
      success: 'Action type deleted successfully',
      error: 'Failed to delete action type'
    }
  },
  category: {
    create: {
      loading: 'Creating category...',
      success: 'Category created successfully',
      error: 'Failed to create category'
    },
    update: {
      loading: 'Updating category...',
      success: 'Category updated successfully',
      error: 'Failed to update category'
    },
    delete: {
      loading: 'Deleting category...',
      success: 'Category deleted successfully',
      error: 'Failed to delete category'
    }
  },
  vendorType: {
    create: {
      loading: 'Creating vendor type...',
      success: 'Vendor type created successfully',
      error: 'Failed to create vendor type'
    },
    update: {
      loading: 'Updating vendor type...',
      success: 'Vendor type updated successfully',
      error: 'Failed to update vendor type'
    },
    delete: {
      loading: 'Deleting vendor type...',
      success: 'Vendor type deleted successfully',
      error: 'Failed to delete vendor type'
    }
  },
  'packaging-option': {
    create: {
      loading: 'Creating packaging option...',
      success: 'Packaging option created successfully',
      error: 'Failed to create packaging option'
    },
    update: {
      loading: 'Updating packaging option...',
      success: 'Packaging option updated successfully',
      error: 'Failed to update packaging option'
    },
    delete: {
      loading: 'Deleting packaging option...',
      success: 'Packaging option deleted successfully',
      error: 'Failed to delete packaging option'
    }
  },
  modifier: {
    create: {
      loading: 'Creating modifier...',
      success: 'Modifier created successfully',
      error: 'Failed to create modifier'
    },
    update: {
      loading: 'Updating modifier...',
      success: 'Modifier updated successfully',
      error: 'Failed to update modifier'
    },
    delete: {
      loading: 'Deleting modifier...',
      success: 'Modifier deleted successfully',
      error: 'Failed to delete modifier'
    }
  },
  lot: {
    create: {
      loading: 'Creating lot...',
      success: 'Lot created successfully',
      error: 'Failed to create lot'
    },
    update: {
      loading: 'Updating lot...',
      success: 'Lot updated successfully',
      error: 'Failed to update lot'
    },
    delete: {
      loading: 'Deleting lot...',
      success: 'Lot deleted successfully',
      error: 'Failed to delete lot'
    }
  },
  section: {
    create: {
      loading: 'Creating section...',
      success: 'Section created successfully',
      error: 'Failed to create section'
    },
    update: {
      loading: 'Updating section...',
      success: 'Section updated successfully',
      error: 'Failed to update section'
    },
    delete: {
      loading: 'Deleting section...',
      success: 'Section deleted successfully',
      error: 'Failed to delete section'
    }
  },
  template: {
    create: {
      loading: 'Creating template...',
      success: 'Template created successfully',
      error: 'Failed to create template'
    },
    update: {
      loading: 'Updating template...',
      success: 'Template updated successfully',
      error: 'Failed to update template'
    },
    delete: {
      loading: 'Deleting template...',
      success: 'Template deleted successfully',
      error: 'Failed to delete template'
    }
  }
};

/**
 * Get toast messages for a specific entity and operation
 */
export function getToastMessages(entity: string, operation: keyof EntityToastMessages): ToastOptions {
  const entityMessages = entityToastMessages[entity];
  const operationMessages = entityMessages?.[operation];
  
  return {
    loading: operationMessages?.loading || defaultMessages[operation]?.loading || 'Processing...',
    success: operationMessages?.success || defaultMessages[operation]?.success || 'Operation completed',
    error: operationMessages?.error || defaultMessages[operation]?.error || 'Operation failed'
  };
}

/**
 * Show a toast promise with entity-specific messages
 */
export function showToastPromise<T>(
  promise: Promise<T>,
  entity: string,
  operation: keyof EntityToastMessages,
  customMessages?: Partial<ToastOptions>
): Promise<T> {
  const messages = getToastMessages(entity, operation);
  
  return toast.promise(promise, {
    loading: customMessages?.loading || messages.loading!,
    success: customMessages?.success || messages.success!,
    error: customMessages?.error || messages.error!
  });
}

/**
 * Show a simple success toast
 */
export function showSuccessToast(message: string): void {
  toast.success(message);
}

/**
 * Show a simple error toast
 */
export function showErrorToast(message: string): void {
  toast.error(message);
}

/**
 * Show a simple info toast
 */
export function showInfoToast(message: string): void {
  toast(message);
}
