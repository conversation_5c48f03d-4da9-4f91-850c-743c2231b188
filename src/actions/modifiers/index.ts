"use server";

import { revalidatePath } from "next/cache";
import { auth } from "@/auth";
import { Modifier, CreateModifierRequest, UpdateModifierRequest } from "@/types/modifiers";
import { api } from "@/lib/api";

// Use a fallback value if the environment variable is not set
const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:44334/v1";

export async function getModifiers() {
  const session = await auth();
  if (!session?.user) {
    throw new Error("Unauthorized");
  }

  // Get vendor ID from session if available
  const vendorId = session.vendor?.id;
  
  // Construct the URL based on whether we have a vendor ID
  const path = vendorId
    ? `vendors/${vendorId}/modifier-options`
    : `modifier-options`;

  console.log(`Fetching modifiers from: ${path}`);
  console.log(`Using token: ${session.accessToken ? 'Token exists' : 'No token'}`);
  console.log(`Token value: ${session.accessToken}`);
  console.log(`Session user: ${JSON.stringify(session.user)}`);
  console.log(`Session vendor: ${JSON.stringify(session.vendor)}`);

  try {
    const modifiers = await api.get<Modifier[]>(path);
    return modifiers;
  } catch (error) {
    console.error("Exception in getModifiers:", error);
    throw error;
  }
}

export async function getModifier(id: string) {
  const session = await auth();
  if (!session?.user) {
    throw new Error("Unauthorized");
  }

  // Get vendor ID from session if available
  const vendorId = session.vendor?.id;
  
  // Construct the URL based on whether we have a vendor ID
  const path = vendorId
    ? `vendors/${vendorId}/modifier-options/${id}`
    : `modifier-options/${id}`;

  try {
    const modifier = await api.get<Modifier>(path);
    return modifier;
  } catch (error) {
    console.error("Exception in getModifier:", error);
    throw error;
  }
}

export async function createModifier(data: CreateModifierRequest): Promise<{ success: boolean; modifier?: Modifier; error?: string }> => {
  const session = await auth();
  if (!session?.user) {
    return {
      success: false,
      error: "Unauthorized"
    };
  }

  // Get vendor ID from session if available
  const vendorId = session.vendor?.id;

  // Construct the URL based on whether we have a vendor ID
  const path = vendorId
    ? `vendors/${vendorId}/modifier-options`
    : `modifier-options`;

  // Transform the data to match the API's expected format
  const apiData = {
    name: data.name,
    type: data.type,
    description: data.description,
    default_price_adjustment: Number(data.default_price_adjustment),
    active: data.active
  };

  console.log("Creating modifier with data:", apiData);

  try {
    const modifier = await api.post<Modifier>(path, apiData);
    revalidatePath("/products/modifiers");
    return { success: true, modifier: modifier || undefined };
  } catch (error) {
    console.error("Exception in createModifier:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create modifier"
    };
  }
}

export async function updateModifier(id: string, data: UpdateModifierRequest): Promise<{ success: boolean; modifier?: Modifier; error?: string }> => {
  const session = await auth();
  if (!session?.user) {
    return {
      success: false,
      error: "Unauthorized"
    };
  }

  // Get vendor ID from session if available
  const vendorId = session.vendor?.id;

  // Construct the URL based on whether we have a vendor ID
  const path = vendorId
    ? `vendors/${vendorId}/modifier-options/${id}`
    : `modifier-options/${id}`;

  // Transform the data to match the API's expected format
  const apiData: Record<string, any> = {};

  if (data.name !== undefined) apiData.name = data.name;
  if (data.type !== undefined) apiData.type = data.type;
  if (data.description !== undefined) apiData.description = data.description;
  if (data.default_price_adjustment !== undefined) apiData.default_price_adjustment = Number(data.default_price_adjustment);
  if (data.active !== undefined) apiData.active = data.active;

  console.log("Updating modifier with data:", apiData);

  try {
    const modifier = await api.put<Modifier>(path, apiData);
    revalidatePath("/products/modifiers");
    return { success: true, modifier: modifier || undefined };
  } catch (error) {
    console.error("Exception in updateModifier:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update modifier"
    };
  }
}

export async function deleteModifier(formData: FormData | string) {
  const session = await auth();
  if (!session?.user) {
    throw new Error("Unauthorized");
  }

  // Extract the ID from FormData if it's a FormData object
  let id: string;
  if (formData instanceof FormData) {
    const idValue = formData.get("id");
    if (!idValue || typeof idValue !== "string") {
      throw new Error("Invalid modifier ID");
    }
    id = idValue;
  } else {
    id = formData;
  }

  // Get vendor ID from session if available
  const vendorId = session.vendor?.id;

  // Construct the base path based on whether we have a vendor ID
  const basePath = vendorId
    ? `vendors/${vendorId}/modifier-options`
    : `modifier-options`;

  try {
    await api.destroy(id, basePath);
    revalidatePath("/products/modifiers");
    return true;
  } catch (error) {
    console.error("Exception in deleteModifier:", error);
    throw error;
  }
} 